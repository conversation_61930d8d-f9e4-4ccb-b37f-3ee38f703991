import '../models/service.dart';
import '../models/notification.dart';
import '../models/errand.dart';
import '../config/api_config.dart';
import 'api_service.dart';

class CustomerService {
  static Future<List<Service>> getServices() async {
    final response = await ApiService.get(ApiConfig.servicesEndpoint);

    if (response['data'] is List) {
      return (response['data'] as List)
          .map((service) => Service.fromJson(service as Map<String, dynamic>))
          .toList();
    }

    throw Exception('Invalid response format');
  }
  
  static Future<Booking> createBooking(CreateBookingRequest request) async {
    final response = await ApiService.post(
      ApiConfig.bookingsEndpoint,
      request.toJson(),
    );
    
    return Booking.fromJson(response['booking']);
  }
  
  static Future<List<Errand>> getBookings() async {
    try {
      final response = await ApiService.get(ApiConfig.bookingsEndpoint);
      
      if (response['data'] is List) {
        return (response['data'] as List)
            .map((booking) => Errand.fromJson(booking))
            .toList();
      }
      
      // Mock data for development
      return _generateMockBookings();
    } catch (e) {
      // Return mock data during development
      return _generateMockBookings();
    }
  }

  static List<Errand> _generateMockBookings() {
    return [
      Errand(
        id: 1,
        title: 'House Cleaning',
        description: 'Complete house cleaning service including all rooms',
        payment: 150.0,
        location: '123 Main St, New York, NY 10001',
        customerId: 1,
        runnerId: 2,
        runnerName: 'Sarah Johnson',
        runnerPhone: '******-567-8901',
        status: ErrandStatus.inProgress,
        scheduledDate: DateTime.now().add(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now(),
        notes: 'Please focus on kitchen and bathrooms',
      ),
      Errand(
        id: 2,
        title: 'Grocery Shopping',
        description: 'Weekly grocery shopping at local supermarket',
        payment: 45.0,
        location: '456 Oak Ave, Brooklyn, NY 11201',
        customerId: 1,
        status: ErrandStatus.requested,
        scheduledDate: DateTime.now().add(const Duration(hours: 4)),
        createdAt: DateTime.now().subtract(const Duration(hours: 3)),
        updatedAt: DateTime.now(),
        notes: 'Shopping list attached',
      ),
      Errand(
        id: 3,
        title: 'Furniture Assembly',
        description: 'IKEA furniture assembly - bed frame and dresser',
        payment: 120.0,
        location: '789 Pine St, Manhattan, NY 10002',
        customerId: 1,
        runnerId: 3,
        runnerName: 'Mike Wilson',
        runnerPhone: '******-678-9012',
        status: ErrandStatus.completed,
        scheduledDate: DateTime.now().subtract(const Duration(days: 3)),
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        notes: 'All tools provided',
      ),
      Errand(
        id: 4,
        title: 'Pet Walking',
        description: '1-hour dog walking service',
        payment: 25.0,
        location: '321 Elm St, Queens, NY 11375',
        customerId: 1,
        runnerId: 4,
        runnerName: 'Emily Davis',
        runnerPhone: '******-789-0123',
        status: ErrandStatus.completed,
        scheduledDate: DateTime.now().subtract(const Duration(days: 1)),
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Errand(
        id: 5,
        title: 'Laundry Service',
        description: 'Wash, dry, and fold laundry service',
        payment: 35.0,
        location: '654 Maple Ave, Bronx, NY 10451',
        customerId: 1,
        status: ErrandStatus.cancelled,
        scheduledDate: DateTime.now().subtract(const Duration(days: 7)),
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        updatedAt: DateTime.now().subtract(const Duration(days: 7)),
        notes: 'Cancelled due to scheduling conflict',
      ),
    ];
  }

  static Future<void> cancelBooking(String bookingId) async {
    try {
      await ApiService.delete('${ApiConfig.bookingsEndpoint}/$bookingId');
    } catch (e) {
      // Mock implementation - in real app this would cancel the booking
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }
  
  static Future<List<AppNotification>> getNotifications() async {
    final response = await ApiService.get(ApiConfig.notificationsEndpoint);

    if (response['data'] is List) {
      return (response['data'] as List)
          .map((notification) => AppNotification.fromJson(notification as Map<String, dynamic>))
          .toList();
    }

    throw Exception('Invalid response format');
  }
  
  static Future<void> markNotificationAsRead(String notificationId) async {
    final endpoint = ApiConfig.markNotificationReadEndpoint.replaceFirst('{id}', notificationId);
    await ApiService.patch(endpoint);
  }
  
  static Future<void> registerDeviceToken(String token) async {
    final request = DeviceTokenRequest(token: token);
    await ApiService.post(
      ApiConfig.deviceTokenEndpoint,
      request.toJson(),
    );
  }
}