import 'dart:io';
import '../models/errand.dart';
import '../models/notification.dart';
import '../config/api_config.dart';
import 'api_service.dart';

class RunnerService {
  static Future<List<Errand>> getAvailableErrands() async {
    final response = await ApiService.get(ApiConfig.availableErrandsEndpoint);

    if (response['data'] is List) {
      return (response['data'] as List)
          .map((errand) => Errand.fromJson(errand as Map<String, dynamic>))
          .toList();
    }

    throw Exception('Invalid response format');
  }
  
  static Future<void> acceptErrand(int errandId) async {
    final endpoint = ApiConfig.acceptErrandEndpoint.replaceFirst('{id}', errandId.toString());
    await ApiService.post(endpoint, {});
  }
  
  static Future<List<RunnerEarning>> getEarningsHistory() async {
    final response = await ApiService.get(ApiConfig.runnerEarningsEndpoint);

    if (response['data'] is List) {
      return (response['data'] as List)
          .map((earning) => RunnerEarning.fromJson(earning as Map<String, dynamic>))
          .toList();
    }

    throw Exception('Invalid response format');
  }

  static Future<List<RunnerEarning>> getEarnings() async {
    try {
      final response = await ApiService.get(ApiConfig.runnerEarningsEndpoint);
      
      if (response['data'] is List) {
        return (response['data'] as List)
            .map((earning) => RunnerEarning.fromJson(earning))
            .toList();
      }
      
      // Mock data for development
      return _generateMockEarnings();
    } catch (e) {
      // Return mock data during development
      return _generateMockEarnings();
    }
  }

  static Future<Map<String, dynamic>> getEarningsStats() async {
    try {
      final response = await ApiService.get('${ApiConfig.runnerEarningsEndpoint}/stats');
      return response;
    } catch (e) {
      // Mock stats for development
      return {
        'total_earnings': 2847.50,
        'week_earnings': 445.75,
        'month_earnings': 1823.25,
        'available_balance': 1245.80,
        'completed_jobs': 47,
        'average_rating': 4.8,
        'total_hours': 156,
      };
    }
  }

  static List<RunnerEarning> _generateMockEarnings() {
    return [
      RunnerEarning(
        id: 1,
        amount: 85.0,
        description: 'House Cleaning - Upper East Side',
        date: DateTime.now().subtract(const Duration(hours: 2)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 2,
        amount: 15.0,
        description: 'Customer tip for excellent service',
        date: DateTime.now().subtract(const Duration(hours: 2, minutes: 30)),
        type: EarningType.tip,
      ),
      RunnerEarning(
        id: 3,
        amount: 65.0,
        description: 'Grocery Shopping - Brooklyn Heights',
        date: DateTime.now().subtract(const Duration(days: 1)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 4,
        amount: 120.0,
        description: 'Furniture Assembly - Manhattan',
        date: DateTime.now().subtract(const Duration(days: 2)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 5,
        amount: 25.0,
        description: 'Weekly completion bonus',
        date: DateTime.now().subtract(const Duration(days: 3)),
        type: EarningType.bonus,
      ),
      RunnerEarning(
        id: 6,
        amount: 45.0,
        description: 'Pet Walking - Central Park',
        date: DateTime.now().subtract(const Duration(days: 4)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 7,
        amount: 10.0,
        description: 'Customer tip - Great work!',
        date: DateTime.now().subtract(const Duration(days: 4, hours: 2)),
        type: EarningType.tip,
      ),
      RunnerEarning(
        id: 8,
        amount: 95.0,
        description: 'Deep Cleaning Service - Queens',
        date: DateTime.now().subtract(const Duration(days: 5)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 9,
        amount: 35.0,
        description: 'Laundry Service - Bronx',
        date: DateTime.now().subtract(const Duration(days: 6)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 10,
        amount: 75.0,
        description: 'Moving Assistance - Staten Island',
        date: DateTime.now().subtract(const Duration(days: 7)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 11,
        amount: 50.0,
        description: 'Monthly performance bonus',
        date: DateTime.now().subtract(const Duration(days: 8)),
        type: EarningType.bonus,
      ),
      RunnerEarning(
        id: 12,
        amount: 55.0,
        description: 'Handyman Services - Brooklyn',
        date: DateTime.now().subtract(const Duration(days: 9)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 13,
        amount: 20.0,
        description: 'Generous customer tip',
        date: DateTime.now().subtract(const Duration(days: 9, hours: 3)),
        type: EarningType.tip,
      ),
      RunnerEarning(
        id: 14,
        amount: 40.0,
        description: 'Yard Work - Long Island',
        date: DateTime.now().subtract(const Duration(days: 10)),
        type: EarningType.job,
      ),
      RunnerEarning(
        id: 15,
        amount: 85.0,
        description: 'Office Cleaning - Midtown',
        date: DateTime.now().subtract(const Duration(days: 11)),
        type: EarningType.job,
      ),
    ];
  }
  
  static Future<void> requestWithdrawal(WithdrawRequest request) async {
    await ApiService.post(
      ApiConfig.runnerWithdrawEndpoint,
      request.toJson(),
    );
  }
  
  static Future<String> uploadDocument(File document) async {
    final response = await ApiService.postMultipart(
      ApiConfig.runnerDocumentsUploadEndpoint,
      document,
      'document',
    );
    
    return response['path'];
  }
  
  static Future<List<Map<String, dynamic>>> getDocuments() async {
    final response = await ApiService.get(ApiConfig.runnerDocumentsEndpoint);

    if (response['data'] is List) {
      return (response['data'] as List).cast<Map<String, dynamic>>();
    }

    throw Exception('Invalid response format');
  }
  
  static Future<void> updateAvailability(List<Map<String, String>> availability) async {
    await ApiService.put(
      ApiConfig.runnerAvailabilityEndpoint,
      {'availability': availability},
    );
  }
  
  static Future<List<AppNotification>> getNotifications() async {
    final response = await ApiService.get(ApiConfig.notificationsEndpoint);
    
    if (response is List) {
      return response.map((notification) => AppNotification.fromJson(notification as Map<String, dynamic>)).toList();
    }
    
    throw Exception('Invalid response format');
  }
  
  static Future<void> markNotificationAsRead(String notificationId) async {
    final endpoint = ApiConfig.markNotificationReadEndpoint.replaceFirst('{id}', notificationId);
    await ApiService.patch(endpoint);
  }
  
  static Future<void> registerDeviceToken(String token) async {
    final request = DeviceTokenRequest(token: token);
    await ApiService.post(
      ApiConfig.deviceTokenEndpoint,
      request.toJson(),
    );
  }
}